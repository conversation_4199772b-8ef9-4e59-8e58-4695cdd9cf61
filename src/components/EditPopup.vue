<template>
  <div class="fixed inset-0 flex items-center justify-center p-12">
    <div class="bg-white p-14 shadow-lg rounded-xl w-96 relative">
      <button
        @click="$emit('cancel')"
        class="absolute top-2 right-2 text-gray-500 hover:text-gray-700"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          class="h-5 w-5"
          viewBox="0 0 20 20"
          fill="currentColor"
        >
          <path
            fill-rule="evenodd"
            d="M10 8.586L3.707 2.293a1 1 0 00-1.414 1.414L8.586 10l-6.293 6.293a1 1 0 001.414 1.414L10 11.414l6.293 6.293a1 1 0 001.414-1.414L11.414 10l6.293-6.293a1 1 0 00-1.414-1.414L10 8.586z"
            clip-rule="evenodd"
          />
        </svg>
      </button>

      <h2 class="!text-xs !font-bold mb-4 !-mt-11 -ml-10">{{ title }}</h2>

      <div class="mb-12">
        <div
          v-for="(item, index) in localItems"
          :key="index"
          class="mb-4 -ml-10"
        >
          <label class="font-bold block text-xs">{{ item.label }}</label>

          <!-- Special case for "Personne à contacter principale" -->
          <template
            v-if="item.label.includes('Personne à contacter principale')"
          >
            <div class="space-y-2 mt-1">
              <div>
                <label class="text-xs text-gray-600">Nom:</label>
                <input
                  v-model="contactInfo.name"
                  type="text"
                  class="border p-1 rounded w-full !text-sm"
                  @input="updateContactInfo(index)"
                />
              </div>
              <div>
                <label class="text-xs text-gray-600">Position:</label>
                <input
                  v-model="contactInfo.position"
                  type="text"
                  class="border p-1 rounded w-full !text-sm"
                  @input="updateContactInfo(index)"
                />
              </div>
              <div>
                <label class="text-xs text-gray-600">Contact:</label>
                <input
                  v-model="contactInfo.contact"
                  type="text"
                  class="border p-1 rounded w-full !text-sm"
                  @input="updateContactInfo(index)"
                />
              </div>
            </div>
          </template>

          <!-- Special case for "Comptes officiels des réseaux sociaux" -->
          <template
            v-else-if="
              item.label.includes('Comptes officiels des réseaux sociaux')
            "
          >
            <div class="space-y-2 mt-1">
              <div>
                <label class="text-xs text-gray-600">LinkedIn:</label>
                <input
                  v-model="socialMedia.linkedin"
                  type="text"
                  class="border p-1 rounded w-full !text-sm"
                  @input="updateSocialMedia(index)"
                />
              </div>
              <div>
                <label class="text-xs text-gray-600">Facebook:</label>
                <input
                  v-model="socialMedia.facebook"
                  type="text"
                  class="border p-1 rounded w-full !text-sm"
                  @input="updateSocialMedia(index)"
                />
              </div>
              <div>
                <label class="text-xs text-gray-600">Twitter:</label>
                <input
                  v-model="socialMedia.twitter"
                  type="text"
                  class="border p-1 rounded w-full !text-sm"
                  @input="updateSocialMedia(index)"
                />
              </div>
            </div>
          </template>

          <!-- Special case for formation hours -->
          <template
            v-else-if="
              Array.isArray(item.value) &&
              item.label.includes('Nombre heures de formation par an')
            "
          >
            <div class="space-y-2 mt-1">
              <div>
                <label class="text-xs text-gray-600">Hommes (heures):</label>
                <input
                  v-model="formationHours.men"
                  type="number"
                  min="0"
                  class="border p-1 rounded w-full !text-sm"
                  @input="updateFormationHours(index)"
                />
              </div>
              <div>
                <label class="text-xs text-gray-600">Femmes (heures):</label>
                <input
                  v-model="formationHours.women"
                  type="number"
                  min="0"
                  class="border p-1 rounded w-full !text-sm"
                  @input="updateFormationHours(index)"
                />
              </div>
            </div>
          </template>

          <!-- Standard array fields (R&D sites, etc.) -->
          <template v-else-if="Array.isArray(item.value)">
            <textarea
              class="border p-1 rounded w-full !text-sm"
              rows="3"
              v-model="item.textValue"
              @input="updateTextValue(index)"
            ></textarea>
          </template>

          <!-- Number fields -->
          <template v-else-if="item.type === 'number'" class="!text-sm">
            <input
              v-model="localItems[index].value"
              type="number"
              min="0"
              :placeholder="item.placeholder || 'Entrez un nombre'"
              class="border p-1 rounded w-full !text-sm"
              @input="validateNumberInput(index)"
            />
            <p v-if="validationErrors[index]" class="text-red-500 text-xs mt-1">
              {{ validationErrors[index] }}
            </p>
          </template>

          <!-- Select/dropdown fields -->
          <template v-else-if="item.type === 'select'" class="!text-sm">
            <select
              v-model="localItems[index].value"
              class="border p-1 rounded w-full !text-sm"
            >
              <option
                v-for="option in item.options"
                :key="option"
                :value="option"
              >
                {{ option }}
              </option>
            </select>
          </template>

          <!-- Standard text fields (fallback) -->
          <template v-else class="!text-sm">
            <input
              v-model="localItems[index].value"
              type="text"
              :placeholder="item.placeholder || ''"
              class="border p-1 rounded w-full !text-sm"
            />
          </template>
        </div>
      </div>

      <div class="absolute bottom-4 right-4">
        <button
          @click="saveChanges"
          class="bg-[#C62027] text-white px-6 py-2 !text-xs !rounded-full shadow-md hover:bg-red-700 transition"
        >
          Sauvegarder
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, defineProps, defineEmits, onMounted } from "vue";

const props = defineProps({
  items: Array,
  title: String,
});
const emit = defineEmits(["save", "cancel"]);

// Create a deep local copy of the items so changes are not immediately reflected upstream.
const localItems = ref(JSON.parse(JSON.stringify(props.items)));

// Track validation errors
const validationErrors = ref({});
const hasValidationErrors = ref(false);

// Initialize contact info and social media objects
const contactInfo = ref({
  name: "",
  position: "",
  contact: "",
});

const socialMedia = ref({
  linkedin: "",
  facebook: "",
  twitter: "",
});

// Initialize formation hours object
const formationHours = ref({
  men: 0,
  women: 0,
});

// On mount, initialize fields
onMounted(() => {
  localItems.value.forEach((item) => {
    // Handle array fields
    if (Array.isArray(item.value)) {
      // Special case for contact info
      if (item.label.includes("Personne à contacter principale")) {
        item.value.forEach((line) => {
          if (line.toLowerCase().startsWith("nom:")) {
            contactInfo.value.name = line.replace(/nom:/i, "").trim();
          } else if (line.toLowerCase().startsWith("position:")) {
            contactInfo.value.position = line.replace(/position:/i, "").trim();
          } else if (line.toLowerCase().startsWith("contact:")) {
            contactInfo.value.contact = line.replace(/contact:/i, "").trim();
          }
        });
      }
      // Special case for social media
      else if (item.label.includes("Comptes officiels des réseaux sociaux")) {
        item.value.forEach((line) => {
          if (line.toLowerCase().startsWith("linkedin:")) {
            socialMedia.value.linkedin = line.replace(/linkedin:/i, "").trim();
          } else if (line.toLowerCase().startsWith("facebook:")) {
            socialMedia.value.facebook = line.replace(/facebook:/i, "").trim();
          } else if (line.toLowerCase().startsWith("twitter:")) {
            socialMedia.value.twitter = line.replace(/twitter:/i, "").trim();
          }
        });
      }
      // Special case for formation hours
      else if (item.label.includes("Nombre heures de formation par an")) {
        item.value.forEach((line) => {
          if (line.toLowerCase().includes("hommes:")) {
            // Extract the number from "Hommes: 232H"
            const hoursMatch = line.match(/(\d+)H/);
            formationHours.value.men = hoursMatch ? parseInt(hoursMatch[1]) : 0;
          } else if (line.toLowerCase().includes("femmes:")) {
            // Extract the number from "Femmes: 322H"
            const hoursMatch = line.match(/(\d+)H/);
            formationHours.value.women = hoursMatch
              ? parseInt(hoursMatch[1])
              : 0;
          }
        });
      }
      // Standard array fields
      else {
        item.textValue = item.value.join("\n");
      }
    }
  });
});

// Update contact info when inputs change
function updateContactInfo(index) {
  const contactArray = [
    `Nom: ${contactInfo.value.name}`,
    `Position: ${contactInfo.value.position}`,
    `Contact: ${contactInfo.value.contact}`,
  ];

  localItems.value[index].value = contactArray;
}

// Update social media when inputs change
function updateSocialMedia(index) {
  const socialArray = [];

  if (socialMedia.value.linkedin) {
    socialArray.push(`LinkedIn: ${socialMedia.value.linkedin}`);
  }

  if (socialMedia.value.facebook) {
    socialArray.push(`Facebook: ${socialMedia.value.facebook}`);
  }

  if (socialMedia.value.twitter) {
    socialArray.push(`Twitter: ${socialMedia.value.twitter}`);
  }

  localItems.value[index].value = socialArray;
}

// Update formation hours when inputs change
function updateFormationHours(index) {
  const formationArray = [
    `Hommes: ${formationHours.value.men}H`,
    `Femmes: ${formationHours.value.women}H`,
  ];

  localItems.value[index].value = formationArray;
}

// Whenever the textarea input changes, update the underlying array value.
function updateTextValue(index) {
  const text = localItems.value[index].textValue;
  localItems.value[index].value = text
    .split("\n")
    .map((s) => s.trim())
    .filter((s) => s.length > 0);
}

// Validate number input to ensure it contains only numeric values
function validateNumberInput(index) {
  const item = localItems.value[index];
  const value = item.value;

  // Clear previous validation error
  validationErrors.value[index] = null;

  // Skip validation if empty (will be handled as null)
  if (!value || value === "") {
    return;
  }

  // Check if the value is a valid number
  if (isNaN(Number(value))) {
    validationErrors.value[index] =
      "Veuillez entrer une valeur numérique valide";
    hasValidationErrors.value = true;
  } else {
    // Check if we need to update the hasValidationErrors flag
    hasValidationErrors.value = Object.values(validationErrors.value).some(
      (error) => error !== null
    );
  }
}

function saveChanges() {
  try {
    // Check for validation errors
    const hasErrors = Object.values(validationErrors.value).some(
      (error) => error !== null
    );

    if (hasErrors) {
      // Don't proceed if there are validation errors
      return;
    }

    // Validate all number fields one more time before saving
    let validationFailed = false;

    localItems.value.forEach((item, index) => {
      if (
        item.type === "number" &&
        item.value !== null &&
        item.value !== undefined &&
        item.value !== ""
      ) {
        if (isNaN(Number(item.value))) {
          validationErrors.value[index] =
            "Veuillez entrer une valeur numérique valide";
          validationFailed = true;
        }
      }
    });

    if (validationFailed) {
      hasValidationErrors.value = true;
      return;
    }

    // For employee KPIs, ensure we're removing the % symbol if present
    localItems.value.forEach((item) => {
      if (item.label === "Hommes" || item.label === "Femmes") {
        // Remove % symbol if present
        if (typeof item.value === "string" && item.value.includes("%")) {
          item.value = item.value.replace("%", "");
        }
      }

      // Clean up number values (remove units like 'M' or '%')
      if (item.type === "number" && typeof item.value === "string") {
        // Extract just the number part
        const match = item.value.match(/(\d+(\.\d+)?)/);
        if (match) {
          item.value = match[0];
        }
      }
    });

    // All validation passed, emit save event
    emit("save", localItems.value);
  } catch (error) {
    // This catch block is just a safety measure
    console.error("Error in EditPopup:", error);
  }
}
</script>
