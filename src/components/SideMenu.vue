<template class="h-full">
  <nav
    class="flex flex-col items-center bg-[#0E1B0F] text-white h-full py-6 w-full"
  >
    <!-- Logo Area -->
    <div class="mb-8 bg-white rounded-full py-2 px-6">
      <img
        src="/TAA_logo.png"
        alt="Logo"
        class="w-30 h-auto mx-auto object-cover"
      />
    </div>
    <!-- Menu Items -->
    <ul class="flex flex-col w-full space-y-4 mr-12 flex-grow">
      <li v-for="item in menuItems" :key="item.name" class="w-full">
        <router-link
          :to="item.path"
          class="flex items-center w-full py-2 rounded transition-colors duration-200 hover:bg-[#1C2D1A] !focus:outline-none !no-underline text-white whitespace-nowrap !text-xs"
          :class="{
            'bg-white !text-[#C62027]': isActive(item.path),
          }"
        >
          <!-- Icon (if provided) -->
          <svg
            v-if="item.icon"
            class="w-5 h-5 ml-1"
            fill="none"
            :key="item.name"
            stroke="currentColor"
            stroke-width="0.5"
            stroke-linecap="round"
            stroke-linejoin="round"
            v-html="item.icon"
          ></svg>
          <!-- Label -->
          <span class="ml-2 !text-xs font-medium truncate flex-grow min-w-0">
            {{ item.label }}
          </span>
        </router-link>
      </li>
      <li class="mt-auto">
        <router-link
          to="/signin"
          class="flex items-center w-full py-2 rounded transition-colors duration-200 hover:bg-[white] hover:!text-[#C62027] !focus:outline-none !no-underline text-white whitespace-nowrap !text-xs"
        >
          <span class="ml-2 !text-lg font-medium truncate flex-grow min-w-0">
            Log out
          </span>
        </router-link>
      </li>
    </ul>
  </nav>
</template>
<script setup>
import { defineProps } from "vue";
import { useRoute } from "vue-router";
const props = defineProps({
  menuItems: {
    type: Array,
  },
});
const route = useRoute();
// Modified isActive to check if the current route starts with the menu item path
const isActive = (path) => {
  return route.path.startsWith(path);
};
</script>
<style scoped>
/* Additional custom styles if needed */
svg path {
  stroke: currentColor !important;
  fill: currentColor !important;
}
</style>
